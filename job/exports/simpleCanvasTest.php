<?php
ini_set('memory_limit', '2048M');
/*
* Copyright (C) Abre.io Inc.
*/

require_once dirname(__FILE__) . '/../../vendor/autoload.php';
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

use GuzzleHttp\Client;
// use Google\Cloud\Storage\StorageClient;

/**
 * Upload data to Google Cloud Storage with organized folder structure
 * 
 * GCS folder layout:
 * - site-id folder: /{date}/site-id/{siteID}/{filename}.json
 * - filename folder: /{date}/filename/{type}/{type}-{siteID}.json
 * 
 * @param array $data Data to upload as JSON
 * @param string $type Type identifier for the data (e.g., 'grades', 'assignments')
 * @param object $bucket GCS bucket object
 * @param string $currentDate Date in Ymd format
 * @param int $siteID Site identifier
 */
// function _uploadToGCS($data, $type, $bucket, $currentDate, $siteID)
// {
//     // disabled for local testing
// }

/**
 * Canvas API Client for Canvas SIS Integration (Skyward data)
 * Handles OAuth2 authentication, API requests, and data processing
 */
class CanvasApiClient
{
    private $baseUrl;
    private $clientId;
    private $clientSecret;
    private $accessToken;
    private $refreshToken;
    private $debugMode;
    private $client;
    
    public function __construct($baseUrl, $clientId, $clientSecret, $refreshToken = null, $debugMode = false)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
        $this->refreshToken = $refreshToken;
        $this->debugMode = $debugMode;
        
        // Initialize Guzzle client once for reuse
        $this->client = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => 60
        ]);
    }
    
    /**
     * Authenticate with Canvas API using OAuth2
     * 
     * @return bool True if authentication successful, false otherwise
     * @throws Exception When authentication fails
     */
    public function authenticate()
    {
        try {
            if ($this->debugMode) {
                error_log("Canvas API: Authenticating with {$this->baseUrl}");
            }
            
            // Use refresh token if available
            if ($this->refreshToken) {
                $response = $this->client->request('POST', '/login/oauth2/token', [
                    'form_params' => [
                        'grant_type' => 'refresh_token',
                        'refresh_token' => $this->refreshToken,
                        'client_id' => $this->clientId,
                        'client_secret' => $this->clientSecret
                    ],
                    'headers' => [
                        'Content-Type' => 'application/x-www-form-urlencoded'
                    ]
                ]);
                
                if ($response->getStatusCode() == 200) {
                    $tokenData = json_decode($response->getBody(), true);
                    $this->accessToken = $tokenData['access_token'];
                    
                    if ($this->debugMode) {
                        error_log("Canvas API: OAuth2 authentication successful");
                    }
                    
                    return true;
                }
            }
            
            throw new Exception("Canvas OAuth2 authentication failed");
            
        } catch (Exception $e) {
            error_log("Canvas API Authentication Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Set access token manually (alternative to OAuth)
     * 
     * @param string $token Access token to set
     */
    public function setAccessToken($token)
    {
        $this->accessToken = $token;
        if ($this->debugMode) {
            error_log("Canvas API: Manual access token set");
        }
    }
    
    /**
     * Make GET request to Canvas API with retry logic
     * 
     * @param string $endpoint API endpoint to call
     * @param array $params Query parameters
     * @param bool $singlePage If true, return a single page without pagination
     * @return array|false API response data or false on failure
     * @throws Exception When API request fails after max retries
     */
    public function get($endpoint, $params = [], $singlePage = false)
    {
        $maxRetries = 3;
        $retryCount = 0;

        while ($retryCount < $maxRetries) {
            try {
                if ($this->debugMode) {
                    error_log("Canvas API GET: {$endpoint} " . json_encode($params));
                }

                $allData = [];
                $currentUrl = $endpoint;
                $currentParams = $params;
                $originalPerPage = $params['per_page'] ?? null;
                $pageCount = 0;
                $maxPages = 10000; // High safety limit - duplicate detection will stop pagination naturally
                $visitedNextUrls = [];
                $isTermsEndpoint = (strpos($endpoint, '/terms') !== false);
                $isCoursesEndpoint = (strpos($endpoint, '/courses') !== false);
                $prevTermsCount = null;
                $seenIds = [];

                do {
                    $pageCount++;
                    if ($pageCount > $maxPages) {
                        error_log("Canvas API: Pagination limit exceeded ({$maxPages} pages). Stopping to prevent infinite loops.");
                        break;
                    }
                    
                    // Set headers dynamically for each request
                    $requestOptions = [
                        'headers' => [
                            'Authorization' => 'Bearer ' . $this->accessToken,
                            'Content-Type' => 'application/json'
                        ]
                    ];
                    
                    // If Link provided an absolute URL, call it verbatim
                    if (strpos($currentUrl, 'http') === 0) {
                        $response = $this->client->request('GET', $currentUrl, $requestOptions);
                    } else {
                        $requestOptions['query'] = $currentParams;
                        $response = $this->client->request('GET', $currentUrl, $requestOptions);
                    }

                    if ($response->getStatusCode() != 200) {
                        throw new Exception("API request failed with status: " . $response->getStatusCode());
                    }

                    $data = json_decode($response->getBody(), true);
                    $stopPaginationNow = false;

                    // Merge list vs wrapper
                    if (is_array($data)) {
                        $isList = empty($data) ? true : array_keys($data) === range(0, count($data) - 1);
                        if ($isList) {
                            // Check for no new data (applies to courses, sections, enrollments, etc.)
                            if ($isCoursesEndpoint || !$isTermsEndpoint) {
                                $pageDataCount = count($data);
                                if ($pageDataCount === 0) {
                                    if ($this->debugMode) {
                                        error_log('Canvas API: empty page; stopping pagination');
                                    }
                                    $stopPaginationNow = true;
                                }
                                
                                // Check for duplicate IDs to detect repeated data
                                $newIds = 0;
                                foreach ($data as $item) {
                                    if (isset($item['id'])) {
                                        $id = $item['id'];
                                        if (!isset($seenIds[$id])) {
                                            $seenIds[$id] = true;
                                            $newIds++;
                                        }
                                    }
                                }
                                
                                if ($pageDataCount > 0 && $newIds === 0) {
                                    if ($this->debugMode) {
                                        error_log('Canvas API: all items on page already seen; stopping pagination');
                                    }
                                    $stopPaginationNow = true;
                                } else if ($this->debugMode && $pageCount % 10 === 0) {
                                    error_log("Canvas API: page {$pageCount}, {$newIds} new items, {$pageDataCount} total items on page");
                                }
                            }
                            $allData = array_merge($allData, $data);
                        } else {
                            if (isset($data['enrollment_terms']) && is_array($data['enrollment_terms'])) {
                                if ($isTermsEndpoint) {
                                    $pageTermsCount = count($data['enrollment_terms']);
                                    if ($pageTermsCount === 0) {
                                        if ($this->debugMode) {
                                            error_log('Canvas terms: empty page; stopping pagination');
                                        }
                                        $stopPaginationNow = true;
                                    }
                                }
                                if (!isset($allData['enrollment_terms'])) {
                                    $allData['enrollment_terms'] = [];
                                }
                                $allData['enrollment_terms'] = array_merge($allData['enrollment_terms'], $data['enrollment_terms']);
                                if ($this->debugMode) {
                                    error_log("Canvas terms merged count: " . count($allData['enrollment_terms']));
                                }
                                if ($isTermsEndpoint) {
                                    $currentTermsCount = count($allData['enrollment_terms']);
                                    if ($prevTermsCount !== null && $currentTermsCount <= $prevTermsCount) {
                                        if ($this->debugMode) {
                                            error_log('Canvas terms: no new terms detected; stopping pagination');
                                        }
                                        $stopPaginationNow = true;
                                    }
                                    $prevTermsCount = $currentTermsCount;
                                }
                            } else {
                                $allData = array_merge($allData, $data);
                            }
                        }
                    }

                    // Skip pagination if single page requested
                    if ($singlePage) {
                        break;
                    }

                    // Pagination via Link header - preserve per_page parameter
                    $linkHeader = $response->getHeader('Link');
                    $nextUrl = null;
                    if ($stopPaginationNow) {
                        // Explicitly stop pagination when no progress is detected (e.g., buggy Link headers)
                        $linkHeader = [];
                    }
                    if (!empty($linkHeader)) {
                        $links = explode(',', $linkHeader[0]);
                        foreach ($links as $link) {
                            if (strpos($link, 'rel="next"') !== false) {
                                preg_match('/<([^>]+)>/', $link, $matches);
                                if (isset($matches[1])) {
                                    $fullNext = $matches[1];
                                    
                                    // If we have a per_page parameter, modify the URL to use it
                                    if ($originalPerPage && strpos($fullNext, 'per_page=') === false) {
                                        $separator = strpos($fullNext, '?') !== false ? '&' : '?';
                                        $fullNext .= $separator . 'per_page=' . $originalPerPage;
                                    }
                                    // Loop protection: stop if we've already seen this URL
                                    if (isset($visitedNextUrls[$fullNext])) {
                                        if ($this->debugMode) {
                                            error_log("Canvas pagination: detected loop on {$fullNext}, stopping");
                                        }
                                        $nextUrl = null;
                                        break;
                                    }
                                    $visitedNextUrls[$fullNext] = true;
                                    $currentUrl = $fullNext;
                                    $currentParams = [];
                                    $nextUrl = $fullNext;
                                    if ($this->debugMode) {
                                        error_log("Canvas pagination: page {$pageCount} => {$currentUrl}");
                                    }
                                }
                                break;
                            }
                        }
                    }
                } while ($nextUrl);

                if ($pageCount > 1 && $this->debugMode) {
                    error_log("Canvas API: Completed pagination through {$pageCount} pages, total records: " . count($allData));
                }

                return $allData;

            } catch (Exception $e) {
                $retryCount++;
                error_log("Canvas API Error (attempt {$retryCount}): " . $e->getMessage());
                if (strpos($e->getMessage(), '401') !== false && $retryCount == 1) {
                    if ($this->debugMode) {
                        error_log("Canvas API: Refreshing token and retrying");
                    }
                    if ($this->authenticate()) {
                        continue;
                    }
                }
                if ($retryCount >= $maxRetries) {
                    error_log("Canvas API: Max retries exceeded for {$endpoint}");
                    return false;
                }
                sleep(pow(2, $retryCount));
            }
        }

        return false;
    }
    
    /**
     * Make GET request to Canvas API for a single page (no pagination)
     * Useful when you only need a limited number of records
     * 
     * @param string $endpoint API endpoint to call
     * @param array $params Query parameters
     * @return array|false API response data or false on failure
     * @throws Exception When API request fails after max retries
     */
    public function getSinglePage($endpoint, $params = [])
    {
        return $this->get($endpoint, $params, true);
    }
}

/**
 * Canvas Grades Processor - handles Canvas API sequence for grades with SIS integration
 */
class CanvasGradesProcessor
{
    // Class constants for configuration values
    private const MAX_COURSES_TO_PROCESS = 250000;
    private const GRADE_BATCH_SIZE = 2000;
    private const COURSE_BATCH_SIZE = 500;
    private const MAX_RETRIES = 3;
    private const PER_PAGE_LIMIT = 1000;
    private const MEMORY_CLEANUP_INTERVAL = 10;
    
    private $canvasApi;
    private $db;
    private $siteId;
    private $schoolYearId;
    private $debugMode;
    private $canvasTermIds;
    private $gradesData = [];
    private $assignmentsData = [];
    private $assignmentDefinitions = [];
    private $jobUuid;
    
    public function __construct($canvasApi, $db, $siteId, $schoolYearId, $debugMode = false, $canvasTermIds = [], $jobUuid = null)
    {
        $this->canvasApi = $canvasApi;
        $this->db = $db;
        $this->siteId = $siteId;
        $this->schoolYearId = $schoolYearId;
        $this->debugMode = $debugMode;
        $this->canvasTermIds = $canvasTermIds;
        $this->jobUuid = $jobUuid;
    }
    
    /**
     * Process Canvas grades following prepInfo.md API sequence
     * 
     * @return int Number of processed grade records
     * @throws Exception When processing fails
     */
    public function processGrades()
    {
        try {
            $processedRecords = 0;
            
            // Step 1: Get account information
            $accounts = $this->canvasApi->get('/api/v1/accounts');
            if ($accounts === false || empty($accounts)) {
                throw new Exception("Failed to retrieve Canvas accounts");
            }
            
            $accountId = $accounts[0]['id'];
            $this->logInfo("Using account ID: {$accountId}");
            
            // Clear existing grades for this site/year once at the beginning
            $this->clearExistingGrades();
            
            // Use DB-provided Canvas term IDs if present; otherwise fallback to API discovery
            if (!empty($this->canvasTermIds)) {
                $processedRecords = $this->processGradesWithDbTerms($accountId);
            } else {
                $processedRecords = $this->processGradesWithApiDiscovery($accountId);
            }
            
            return $processedRecords;
            
        } catch (Exception $e) {
            $this->logError("Grades Processing Error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Process grades using database-provided term IDs
     * 
     * @param int $accountId Canvas account ID
     * @return int Number of processed records
     */
    private function processGradesWithDbTerms($accountId)
    {
        $processedRecords = 0;
        $totalCourses = 0;
        
        // Build account + subaccount list
        $accountIds = $this->buildAccountIdList($accountId);
        
        foreach ($this->canvasTermIds as $termId) {
            $this->logInfo("Processing term id {$termId} from DB");
            $courses = $this->discoverCoursesForTerm($accountIds, $termId);
            
            if (!empty($courses)) {
                $totalCourses += count($courses);
                $processedRecords += $this->processCourseGrades($courses);
            }
        }
        
        $this->logInfo("Total courses across DB terms: {$totalCourses}");
        return $processedRecords;
    }
    
    /**
     * Process grades using API discovery of terms
     * 
     * @param int $accountId Canvas account ID
     * @return int Number of processed records
     */
    private function processGradesWithApiDiscovery($accountId)
    {
        $termsResponse = $this->canvasApi->get("/api/v1/accounts/{$accountId}/terms");
        if ($termsResponse === false) {
            throw new Exception("Failed to retrieve Canvas terms");
        }
        
        $terms = $termsResponse['enrollment_terms'] ?? [];
        $this->logInfo("Found " . count($terms) . " terms");

        // Build account + subaccount list once
        $accountIds = $this->buildAccountIdList($accountId);
        $processedRecords = 0;
        $totalCourses = 0;
        
        // Process ALL terms that have course data, not just one
        foreach ($terms as $term) {
            if ($term['name'] === 'X MASS ARCHIVE') {
                continue; // Skip archive terms
            }
            
            $this->logInfo("Processing term: {$term['name']}");
            $courses = $this->discoverCoursesForTerm($accountIds, $term['id']);
            
            if (!empty($courses)) {
                $this->logInfo("Found " . count($courses) . " courses in term: {$term['name']}");
                $totalCourses += count($courses);
                $processedRecords += $this->processCourseGrades($courses);
            } else {
                $this->logInfo("No courses found in term: {$term['name']}");
            }
        }
        
        $this->logInfo("Total courses across all terms: {$totalCourses}");
        return $processedRecords;
    }
    
    /**
     * Build list of account IDs including subaccounts
     * 
     * @param int $accountId Main account ID
     * @return array List of account IDs
     */
    private function buildAccountIdList($accountId)
    {
        $accountIds = [$accountId];
        
        // Try to get sub_accounts, but continue if access is denied
        $subs = $this->canvasApi->get("/api/v1/accounts/{$accountId}/sub_accounts", [
            'recursive' => true,
            'per_page' => self::PER_PAGE_LIMIT
        ]);
        
        if ($subs === false) {
            // API call failed (likely due to insufficient scopes)
            $this->logInfo("Warning: Cannot access sub_accounts (likely insufficient scopes)");
            $this->logInfo("Continuing with main account ID only: {$accountId}");
        } elseif (is_array($subs)) {
            foreach ($subs as $sa) {
                if (!empty($sa['id'])) {
                    $accountIds[] = $sa['id'];
                }
            }
        }
        
        return $accountIds;
    }
    
    /**
     * Discover courses for a specific term across multiple accounts
     * 
     * @param array $accountIds List of account IDs to search
     * @param int $termId Term ID to search for
     * @return array List of discovered courses
     */
    private function discoverCoursesForTerm($accountIds, $termId)
    {
        $courses = [];
        $seen = [];
        
        foreach ($accountIds as $accId) {
            // Pass 1: available courses - use full pagination to get ALL courses
            $batchA = $this->canvasApi->get("/api/v1/accounts/{$accId}/courses", [
                'enrollment_term_id' => $termId,
                'per_page' => self::PER_PAGE_LIMIT,
                'state[]' => 'available'
            ]);
            if (is_array($batchA)) {
                foreach ($batchA as $c) {
                    if (!empty($c['id']) && !isset($seen[$c['id']])) {
                        $seen[$c['id']] = true;
                        $courses[] = $c;
                    }
                }
            }
            
            // Pass 2: completed courses - use full pagination to get ALL courses
            $batchC = $this->canvasApi->get("/api/v1/accounts/{$accId}/courses", [
                'enrollment_term_id' => $termId,
                'per_page' => self::PER_PAGE_LIMIT,
                'state[]' => 'completed'
            ]);
            if (is_array($batchC)) {
                foreach ($batchC as $c) {
                    if (!empty($c['id']) && !isset($seen[$c['id']])) {
                        $seen[$c['id']] = true;
                        $courses[] = $c;
                    }
                }
            }
        }
        
        return $courses;
    }
    
    /**
     * Find active term with actual course data
     * 
     * @param int $accountId Canvas account ID
     * @param array $terms List of available terms
     * @return array|null Term data or null if none found
     */
    private function findActiveTermWithData($accountId, $terms)
    {
        $currentTerm = null;
        $maxCourses = 0;
        
        // Test each term to find one with courses
        foreach ($terms as $term) {
            if ($term['name'] === 'X MASS ARCHIVE') {
                continue; // Skip archive
            }
            
            $testCourses = $this->canvasApi->get("/api/v1/accounts/{$accountId}/courses", [
                'enrollment_term_id' => $term['id'],
                'state[]' => 'available',
                'per_page' => 5 // Just test
            ]);
            
            if ($testCourses !== false) {
                $courseCount = count($testCourses);
                if ($this->debugMode) {
                    $this->logInfo("Term '{$term['name']}' has {$courseCount} courses");
                }
                
                if ($courseCount > $maxCourses) {
                    $maxCourses = $courseCount;
                    $currentTerm = $term;
                }
            }
        }
        
        return $currentTerm;
    }
    
    /**
     * Process courses and extract grades with SIS integration using batch processing
     * 
     * @param array $courses List of courses to process
     * @return int Number of processed records
     */
    private function processCourseGrades($courses)
    {
        $processedRecords = 0;
        $coursesProcessed = 0;
        
        // Filter courses more aggressively before processing
        $activeCourses = $this->filterActiveCourses($courses);
        $this->logInfo("Filtered to " . count($activeCourses) . " active courses from " . count($courses) . " total");
        
        // Process active courses in parallel batches for better performance
        $courseBatches = array_chunk($activeCourses, self::COURSE_BATCH_SIZE);
        foreach ($courseBatches as $batchIndex => $courseBatch) {
            $this->logInfo("Processing course batch " . ($batchIndex + 1) . " of " . count($courseBatches));
            
            foreach ($courseBatch as $course) {
                if ($coursesProcessed >= self::MAX_COURSES_TO_PROCESS) {
                    break 2; // Break out of both loops
                }
                
                $courseId = $course['id'];
                $courseName = $course['name'];
                
                $this->logInfo("Processing course: {$courseName} (ID: {$courseId})");
                
                // Collect assignments first (needed for enrollment processing)
                $this->collectAssignmentData($course);

                // Process course sections and grades with optimizations
                $processedRecords += $this->processCourseSectionsOptimized($course);
                
                $coursesProcessed++;
            }
            
            // Memory cleanup between batches
            gc_collect_cycles();
        }
        
        $this->logInfo("Processed {$coursesProcessed} courses in " . count($courseBatches) . " batches");
        return $processedRecords;
    }
    
    /**
     * Process sections for a specific course with optimizations
     * 
     * @param array $course Course data
     * @return int Number of processed records
     */
    private function processCourseSectionsOptimized($course)
    {
        $processedRecords = 0;
        $courseId = $course['id'];
        
        // Get sections with pre-loaded enrollments for efficiency
        $sections = $this->canvasApi->get("/api/v1/courses/{$courseId}/sections", [
            'per_page' => self::PER_PAGE_LIMIT,
            'include[]' => 'enrollments'
        ]);
        
        if ($sections !== false) {
            // Separate sections that have enrollments vs those that need fetching
            $sectionsNeedingEnrollments = [];
            $sectionsWithEnrollments = [];
            
            foreach ($sections as $section) {
                if (isset($section['enrollments'])) {
                    $sectionsWithEnrollments[] = $section;
                } else {
                    $sectionsNeedingEnrollments[] = $section;
                }
            }
            
            // Process sections that already have enrollments
            foreach ($sectionsWithEnrollments as $section) {
                $enrollments = array_filter($section['enrollments'], function($e) {
                    return $e['type'] === 'StudentEnrollment';
                });
                
                if (count($enrollments) > 0) {
                    $processedRecords += $this->processEnrollmentGrades($enrollments, $course, $section);
                }
            }
            
            // Process sections that need enrollment fetching in batches
            if (!empty($sectionsNeedingEnrollments)) {
                $this->logInfo("Fetching enrollments for " . count($sectionsNeedingEnrollments) . " sections in batches");
                
                $sectionBatches = array_chunk($sectionsNeedingEnrollments, 10); // Process 10 sections at a time
                foreach ($sectionBatches as $batchIndex => $sectionBatch) {
                    $this->logInfo("Processing section batch " . ($batchIndex + 1) . " of " . count($sectionBatches));
                    
                    foreach ($sectionBatch as $section) {
                        $sectionId = $section['id'];
                        
                        $enrollments = $this->canvasApi->get("/api/v1/sections/{$sectionId}/enrollments", [
                            'type[]' => 'StudentEnrollment',
                            'include[]' => 'grades',
                            'state[]' => 'active',
                            'per_page' => self::PER_PAGE_LIMIT
                        ]);
                        
                        if ($enrollments !== false && count($enrollments) > 0) {
                            $processedRecords += $this->processEnrollmentGrades($enrollments, $course, $section);
                        }
                    }
                    
                    // Small delay between batches to be nice to the API :)
                    if ($batchIndex < count($sectionBatches) - 1) {
                        usleep(100000); // 0.1 second pause
                    }
                }
            }
        }
        
        return $processedRecords;
    }
    
    /**
     * Process sections for a specific course (legacy method for backward compatibility)
     * 
     * @param array $course Course data
     * @return int Number of processed records
     */
    private function processCourseSections($course)
    {
        return $this->processCourseSectionsOptimized($course);
    }
    
    /**
     * Collect assignment data for a course
     * 
     * @param array $course Course data
     */
    private function collectAssignmentData($course)
    {
        $courseId = $course['id'];
        $courseCode = $course['course_code'] ?? $courseId;

        // Get assignments
        $assignments = $this->canvasApi->get("/api/v1/courses/{$courseId}/assignments", [
            'per_page' => self::PER_PAGE_LIMIT
        ]);

        if (!is_array($assignments) || empty($assignments)) {
            return; // No assignments to process
        }

        // Store assignment definitions for this course
        if (!isset($this->assignmentDefinitions[$courseCode])) {
            $this->assignmentDefinitions[$courseCode] = [];
        }

        foreach ($assignments as $a) {
            if (isset($a['id'])) {
                $this->assignmentDefinitions[$courseCode][$a['id']] = [
                    'assignment_id' => $a['id'],
                    'assignment_name' => $a['name'] ?? '',
                    'points_possible' => $a['points_possible'] ?? null
                ];
            }
        }

        // Try to get submissions to match with assignments
        $submissions = $this->canvasApi->get("/api/v1/courses/{$courseId}/students/submissions", [
            'student_ids[]' => 'all',
            'include[]' => 'assignment',
            'per_page' => self::PER_PAGE_LIMIT
        ]);

        // Store submissions keyed by assignment_id and user_id for easy lookup
        if (is_array($submissions) && !empty($submissions)) {
            foreach ($submissions as $s) {
                $aid = $s['assignment_id'] ?? null;
                $uid = $s['user_id'] ?? null;
                if ($aid && $uid && isset($this->assignmentDefinitions[$courseCode][$aid])) {
                    $this->assignmentDefinitions[$courseCode][$aid]['submissions'][$uid] = [
                        'score' => $s['score'] ?? null
                    ];
                }
            }
        }
    }
    
    /**
     * Process enrollment grades and insert into abre_grades
     * 
     * @param array $enrollments List of enrollments
     * @param array $course Course data
     * @param array $section Section data
     * @return int Number of processed records
     */
    private function processEnrollmentGrades($enrollments, $course, $section)
    {
        $processedCount = 0;
        $gradeRecords = [];
        
        foreach ($enrollments as $enrollment) {
            $studentSisId = $enrollment['user']['sis_user_id'] ?? null;
            $studentCanvasId = $enrollment['user_id'];
            
            // Skip if no SIS ID (not a Skyward student)
            if (!$studentSisId) {
                continue;
            }
            
            // Extract grade data
            $currentGrade = $enrollment['grades']['current_grade'] ?? null;
            $currentScore = $enrollment['grades']['current_score'] ?? null;
            $finalGrade = $enrollment['grades']['final_grade'] ?? null;
            $finalScore = $enrollment['grades']['final_score'] ?? null;
            
            $gradeRecord = [
                'student_id' => $studentSisId,
                'course_code' => $course['course_code'] ?? $course['id'],
                'section_code' => $section['sis_section_id'] ?? $section['id'],
                'school_code' => '',
                'staff_id' => '',
                'term_code' => '',
                'period' => '1',
                'class_name' => $course['name'],
                'teacher_name' => '',
                'letter_grade' => $currentGrade,
                'percentage' => $currentScore,
                'performance' => ''
            ];

            $this->gradesData[] = $gradeRecord;
            $gradeRecords[] = $gradeRecord;

            // Create assignment records for this student
            $courseCode = $course['course_code'] ?? $course['id'];
            if (isset($this->assignmentDefinitions[$courseCode])) {
                foreach ($this->assignmentDefinitions[$courseCode] as $assignment) {
                    $score = null;
                    if (isset($assignment['submissions'][$studentCanvasId])) {
                        $score = $assignment['submissions'][$studentCanvasId]['score'];
                    }

                    $this->assignmentsData[] = [
                        'course_code' => $courseCode,
                        'course_name' => $course['name'] ?? '',
                        'assignment_id' => $assignment['assignment_id'],
                        'assignment_name' => $assignment['assignment_name'],
                        'user_id' => $studentSisId, // Use SIS ID for database
                        'score' => $score,
                        'points_possible' => $assignment['points_possible']
                    ];
                }

            }

            $processedCount++;
            
            // Insert in batches
            if (count($gradeRecords) >= self::GRADE_BATCH_SIZE) {
                $this->insertGradesBatch($gradeRecords);
                $gradeRecords = [];
            }
        }
        
        // Insert remaining records
        if (!empty($gradeRecords)) {
            $this->insertGradesBatch($gradeRecords);
        }
        
        return $processedCount;
    }

    /**
     * Get collected grades data
     * 
     * @return array Grades data
     */
    public function getGradesData()
    {
        return $this->gradesData;
    }

    /**
     * Get collected assignments data
     * 
     * @return array Assignments data
     */
    public function getAssignmentsData()
    {
        return $this->assignmentsData;
    }
    
    /**
     * Insert batch of grade records into abre_grades using prepared statements
     * 
     * @param array $gradeRecords Array of grade records to insert
     * @throws Exception When database operation fails
     */
    private function insertGradesBatch($gradeRecords)
    {
        if (empty($gradeRecords)) {
            return;
        }
        
        try {
            // Use the same approach as the main Canvas integration
            $gradeValues = [];
            foreach ($gradeRecords as $record) {
                $studentIdEscaped = $this->db->real_escape_string($record['student_id']);
                $courseCodeEscaped = $this->db->real_escape_string($record['course_code']);
                $sectionCodeEscaped = $this->db->real_escape_string($record['section_code']);
                $classNameEscaped = $this->db->real_escape_string($record['class_name']);
                $letterGradeEscaped = $this->db->real_escape_string($record['letter_grade'] ?? '');
                $percentage = is_numeric($record['percentage']) ? (float)$record['percentage'] : 'NULL';
                
                $gradeValues[] = "('{$studentIdEscaped}', '{$courseCodeEscaped}', '{$sectionCodeEscaped}', '', '', '', '1', '{$classNameEscaped}', '', '{$letterGradeEscaped}', {$percentage}, '', {$this->siteId}, {$this->schoolYearId})";
            }
            
            if (!empty($gradeValues)) {
                $gradeColumns = 'REPLACE INTO abre_grades (
                    student_id, course_code, section_code, school_code, staff_id, term_code, period,
                    class_name, teacher_name, letter_grade, percentage, performance, site_id, school_year_id
                ) VALUES ';
                
                $gradeChunks = array_chunk($gradeValues, 1000);
                foreach ($gradeChunks as $chunk) {
                    $sql = $gradeColumns . implode(',', $chunk);
                    if (!$this->db->query($sql)) {
                        throw new Exception("Failed to insert grades: " . $this->db->error);
                    }
                }
                
                if ($this->debugMode) {
                    $this->logInfo("Inserted " . count($gradeRecords) . " grade records");
                }
            }
            
        } catch (Exception $e) {
            $this->logError("Failed to insert grades batch: " . $e->getMessage());
            throw new Exception("Database insert failed: " . $e->getMessage());
        }
    }
    
    /**
     * Clear existing grades for this site/year
     * 
     * @throws Exception When database operation fails
     */
    private function clearExistingGrades()
    {
        // Both prod and local use abre_grades
        $sql = 'DELETE FROM abre_grades WHERE site_id = ? AND school_year_id = ?';
        $stmt = $this->db->prepare($sql);
        $stmt->bind_param("ii", $this->siteId, $this->schoolYearId);
        
        if (!$stmt->execute()) {
            throw new Exception("Failed to clear existing grades: " . $stmt->error);
        }
        
        $stmt->close();
        $this->logInfo("Cleared existing grades for site {$this->siteId}");
    }
    
    /**
     * Filter courses to only active, relevant courses
     * 
     * @param array $courses Array of course data
     * @return array Filtered array of active courses
     */
    private function filterActiveCourses($courses)
    {
        $activeCourses = [];
        $skippedTemplate = 0;
        $skippedInactive = 0;
        
        foreach ($courses as $course) {
            // Skip template/sample courses
            if ($this->isTemplateCourse($course['name'])) {
                $skippedTemplate++;
                continue;
            }
            
            // Skip courses that are likely empty (workflow_state check)
            if (isset($course['workflow_state']) && 
                in_array($course['workflow_state'], ['deleted', 'completed', 'concluded'])) {
                $skippedInactive++;
                continue;
            }
            
            $activeCourses[] = $course;
        }
        
        $this->logInfo("Filtered courses: {$skippedTemplate} templates, {$skippedInactive} inactive");
        return $activeCourses;
    }
    
    /**
     * Check if course is a template/sample course
     * 
     * @param string $courseName Course name to check
     * @return bool True if template course, false otherwise
     */
    private function isTemplateCourse($courseName)
    {
        $templateKeywords = ['template', 'sample', 'example', 'test', 'demo', 'sandbox'];
        $lowerName = strtolower($courseName);
        
        foreach ($templateKeywords as $keyword) {
            if (strpos($lowerName, $keyword) !== false) {
                if ($this->debugMode) {
                    $this->logInfo("Skipping template course: {$courseName}");
                }
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Log info message with context
     * 
     * @param string $message Message to log
     */
    private function logInfo($message)
    {
        $context = "Site: {$this->siteId}, Year: {$this->schoolYearId}";
        if ($this->jobUuid) {
            $context .= ", Job: {$this->jobUuid}";
        }
        error_log("Canvas: {$message} [{$context}]");
    }
    
    /**
     * Log error message with context
     * 
     * @param string $message Error message to log
     */
    private function logError($message)
    {
        $context = "Site: {$this->siteId}, Year: {$this->schoolYearId}";
        if ($this->jobUuid) {
            $context .= ", Job: {$this->jobUuid}";
        }
        error_log("Canvas: {$message} [{$context}]");
    }
}

/**
 * Run the Canvas Skyward integration job
 * 
 * @param object $db Database connection
 * @param int $siteID Site identifier
 * @param object $config Configuration object
 * @return void
 */


// Database connection - LOCAL ONLY
$db_host = "127.0.0.1:3306";
$db_user = "root";
$db_password = "root";
$db_name = "Abre";

$db = new mysqli($db_host, $db_user, $db_password, $db_name);
if ($db->connect_error) {
    die("DB Connection failed: " . $db->connect_error);
}

// Canvas config - LOCAL TESTING
$canvasConfig = [
    'baseUrl' => 'https://wayzata.instructure.com',
    'clientId' => '46400000000000260',
    'clientSecret' => 'YPyF7cmTB88PuCTKCZeCzQNYkEt7JXN8hWK27JnmJWv83UNwTtntrt3DwDzy9fKA',
    'refreshToken' => '4640~yJNfc3kwvtDkwvwyf9ffeD6zrTGEvLcP7TRPR2nk2z7GFYuHax2F67WYF4RPtGfn',
    'siteId' => 357,
    'schoolYearId' => 7
];
// 
//function Job($db, $siteID, $config)
//{
    ignore_user_abort(true);
    set_time_limit(0);
    
    // $cronName = 'Canvas Skyward Integration';
    
    try {
        $uuid = null;
        // $uuid = Logger::logCronStart($db, $siteID, $cronName);
        
        error_log("=== Canvas Skyward Integration Started ===");
        // Local test: set siteID from local config
        $siteID = $canvasConfig['siteId'];
        error_log("Site ID: {$siteID}");
        
        // Get current school year
        // Local test: allow override from local config
        $schoolYearID = $canvasConfig['schoolYearId'] ?? getCurrentSchoolYearID($db);
        error_log("School Year ID: {$schoolYearID}");
        
        // Initialize Canvas API client
        // Local test: build client from local config
        $canvasApi = new CanvasApiClient(
            $canvasConfig['baseUrl'],
            $canvasConfig['clientId'],
            $canvasConfig['clientSecret'],
            $canvasConfig['refreshToken'] ?? null,
            false
        );
        
        // Set manual access token if provided
        // if (isset($config->canvas->accessToken)) {
        //     $canvasApi->setAccessToken($config->canvas->accessToken);
        // }
        
        // Authenticate
        if (!$canvasApi->authenticate()) {
            throw new Exception("Failed to authenticate with Canvas API");
        }
        
        error_log('Canvas: Authentication successful');

        // Align with local: clear existing abre_grades and optionally abre_assignments for this site/year
        if ($stmt = $db->prepare('DELETE FROM abre_grades WHERE site_id = ? AND school_year_id = ?')) {
            $stmt->bind_param('ii', $siteID, $schoolYearID);
            $stmt->execute();
            $stmt->close();
        }

        $assignmentsTableExists = false;
        $assignmentsTableName = 'abre_assignments';
        if ($result = $db->query("SHOW TABLES LIKE '" . $assignmentsTableName . "'")) {
            $assignmentsTableExists = ($result->num_rows > 0);
            $result->close();
        }
        if ($assignmentsTableExists) {
            if ($stmt = $db->prepare('DELETE FROM ' . $assignmentsTableName . ' WHERE site_id = ? AND school_year_id = ?')) {
                $stmt->bind_param('ii', $siteID, $schoolYearID);
                $stmt->execute();
                $stmt->close();
            }
            error_log('Cleared ' . $assignmentsTableName);
        } else {
            error_log('WARNING: ' . $assignmentsTableName . ' table does not exist - assignments will not be processed');
        }

        // Load Canvas term IDs from DB (prod)
        $canvasTermIds = [];
        if ($stmt = $db->prepare("SELECT canvas_term_id FROM vendor_canvas_term_map WHERE site_id = ? AND school_year_id = ?")) {
            $stmt->bind_param('ii', $siteID, $schoolYearID);
            if ($stmt->execute()) {
                $termId = null;
                $stmt->bind_result($termId);
                while ($stmt->fetch()) {
                    $canvasTermIds[] = (int)$termId;
                }
            }
            $stmt->close();
        }
        error_log("Canvas: Loaded " . count($canvasTermIds) . " DB term IDs for site {$siteID} year {$schoolYearID}");

        // Initialize grades processor
        $gradesProcessor = new CanvasGradesProcessor(
            $canvasApi,
            $db,
            $siteID,
            $schoolYearID,
            false, // Debug mode off for production
            $canvasTermIds,
            $uuid
        );
        
        // Process grades
        $processedRecords = $gradesProcessor->processGrades();

        // Insert into abre_grades to match local behavior
        $gradesPayload = $gradesProcessor->getGradesData();
        if (is_array($gradesPayload) && !empty($gradesPayload)) {
            $gradeValues = [];
            foreach ($gradesPayload as $record) {
                // Use the correct field names from the updated schema
                $studentIdEscaped = $db->escape_string((string) ($record['student_id'] ?? ''));
                $courseCodeEscaped = $db->escape_string((string) ($record['course_code'] ?? ''));
                $sectionCodeEscaped = $db->escape_string((string) ($record['section_code'] ?? ''));
                $classNameEscaped = $db->escape_string((string) ($record['class_name'] ?? ''));
                $letterGradeEscaped = $db->escape_string((string) ($record['letter_grade'] ?? ''));
                $currentScore = isset($record['percentage']) && $record['percentage'] !== null ? (float) $record['percentage'] : 0;

                $gradeValues[] = "('{$studentIdEscaped}', '{$courseCodeEscaped}', '{$sectionCodeEscaped}', '', '', '', '1', '{$classNameEscaped}', '', '{$letterGradeEscaped}', {$currentScore}, '', {$siteID}, {$schoolYearID})";
            }
            if (!empty($gradeValues)) {
                $gradeColumns = 'REPLACE INTO abre_grades (student_id, course_code, section_code, school_code, staff_id, term_code, period, class_name, teacher_name, letter_grade, percentage, performance, site_id, school_year_id) VALUES ';
                $gradeChunks = array_chunk($gradeValues, 1000);
                foreach ($gradeChunks as $chunk) {
                    $sql = $gradeColumns . implode(',', $chunk);
                    if (!$db->query($sql)) {
                        error_log('Failed to insert grades: ' . $db->error);
                        break;
                    }
                }
            }
        }

        // Insert into abre_assignments to match local behavior (if table exists)
        $assignmentsPayload = $gradesProcessor->getAssignmentsData();
        error_log("Assignment payload count: " . (is_array($assignmentsPayload) ? count($assignmentsPayload) : "0"));
        
        if ($assignmentsTableExists && is_array($assignmentsPayload) && !empty($assignmentsPayload)) {
            try {
                error_log("Starting assignment insert...");
                $assignmentValues = [];
                                foreach ($assignmentsPayload as $a) {
                    $studentIdEscaped = $db->escape_string((string) ($a['user_id'] ?? ''));
                    $courseCodeEscaped = $db->escape_string((string) ($a['course_code'] ?? ''));
                    $sectionCodeEscaped = $db->escape_string(''); // Default empty
                    $schoolCodeEscaped = $db->escape_string(''); // Default empty
                    $staffIdEscaped = $db->escape_string(''); // Default empty
                    $termCodeEscaped = $db->escape_string(''); // Default empty
                    $titleEscaped = $db->escape_string((string) ($a['assignment_name'] ?? ''));
                    $descriptionEscaped = $db->escape_string(''); // Default empty
                    $dueDateEscaped = $db->escape_string(''); // Default empty
                    $categoryEscaped = $db->escape_string(''); // Default empty
                    $earnedPoints = isset($a['score']) && $a['score'] !== null ? (float) $a['score'] : 'NULL';
                    $possiblePoints = isset($a['points_possible']) && $a['points_possible'] !== null ? (float) $a['points_possible'] : 'NULL';
                    $weightPercentage = 'NULL'; // Default NULL
                    $commentEscaped = $db->escape_string(''); // Default empty
                    $published = 1; // Default to published

                    $assignmentValues[] = "('{$studentIdEscaped}', '{$courseCodeEscaped}', '{$sectionCodeEscaped}', '{$schoolCodeEscaped}', '{$staffIdEscaped}', '{$termCodeEscaped}', '1', '{$titleEscaped}', '{$descriptionEscaped}', '{$dueDateEscaped}', '{$categoryEscaped}', {$earnedPoints}, {$possiblePoints}, {$weightPercentage}, '{$commentEscaped}', {$published}, {$siteID}, {$schoolYearID})";
                }
                if (!empty($assignmentValues)) {
                    $assignmentColumns = 'REPLACE INTO ' . $assignmentsTableName . ' (student_id, course_code, section_code, school_code, staff_id, term_code, period, title, description, due_date, category, earned_points, possible_points, weight_percentage, comment, published, site_id, school_year_id) VALUES ';
                    $assignmentChunks = array_chunk($assignmentValues, 2000);
                    foreach ($assignmentChunks as $chunk) {
                        $sql = $assignmentColumns . implode(',', $chunk);
                        if (!$db->query($sql)) {
                            error_log('Failed to insert assignments: ' . $db->error);
                            error_log('Failed SQL: ' . substr($sql, 0, 200) . '...');
                            break;
                        } else {
                            error_log('Successfully inserted ' . count($chunk) . ' assignment records');
                        }
                    }
                } // Close if (!empty($assignmentValues))
            } catch (Exception $e) {
                error_log('Assignment insert failed: ' . $e->getMessage());
            }
        }

        // Local test: disable GCS uploads
        // try {
        //     $storage = new StorageClient(['projectId' => 'abre-production']);
        //     $bucketName = 'prd-landing-zone';
        //     $bucket = $storage->bucket($bucketName);
        //     $currentDate = date('Ymd');
        //
        //     _uploadToGCS($gradesPayload, 'grades', $bucket, $currentDate, $siteID);
        //     _uploadToGCS($assignmentsPayload, 'assignments', $bucket, $currentDate, $siteID);
        // } catch (Exception $e) {
        //     error_log('GCS upload failed: ' . $e->getMessage());
        // }
        
        error_log("Canvas: Processed {$processedRecords} grade records");
        error_log("=== Canvas Skyward Integration Complete ===");
        
        $status = CRON_SUCCESS;
        $details = [
            'gradesProcessed' => $processedRecords,
            'schoolYearId' => $schoolYearID
        ];
        
    } catch (Exception $ex) {
        $error = $ex->getMessage();
        error_log("Canvas Skyward Integration Error: " . $error);
        
        $status = CRON_FAILURE;
        $details = ['error' => $error];
    }
    
    // Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
//}
?>


