<?php

/*
* Copyright (C) Abre.io Inc.
*/

require_once dirname(__FILE__) . '/../../vendor/autoload.php';
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

use GuzzleHttp\Client;
use Google\Cloud\Storage\StorageClient;

/**
 * Upload data to Google Cloud Storage with organized folder structure
 * 
 * GCS folder layout:
 * - site-id folder: /{date}/site-id/{siteID}/{filename}.json
 * - filename folder: /{date}/filename/{type}/{type}-{siteID}.json
 * 
 * @param array $data Data to upload as JSON
 * @param string $type Type identifier for the data (e.g., 'grades', 'assignments')
 * @param object $bucket GCS bucket object
 * @param string $currentDate Date in Ymd format
 * @param int $siteID Site identifier
 */
function _uploadToGCS($data, $type, $bucket, $currentDate, $siteID)
{
    try {
        if (empty($data)) {
            error_log('No data to upload for type: ' . $type);
            return;
        }

        $jsonEncoded = json_encode($data);
        if ($jsonEncoded === false) {
            error_log('Failed to encode JSON for type: ' . $type);
            return;
        }

        // Upload to site-id folder
        $tempFile1 = tmpfile();
        fwrite($tempFile1, $jsonEncoded);
        rewind($tempFile1);
        $fileName = 'Abre_Canvas_Skyward_' . $type . '.json';
        $bucket->upload($tempFile1, [
            'name' => $currentDate . '/site-id/' . $siteID . '/' . $fileName
        ]);
        fclose($tempFile1);

        // Upload to filename folder
        $tempFile2 = tmpfile();
        fwrite($tempFile2, $jsonEncoded);
        rewind($tempFile2);
        $folderName = 'Abre_Canvas_Skyward_' . $type;
        $bucket->upload($tempFile2, [
            'name' => $currentDate . '/filename/' . $folderName . '/' . $folderName . '-' . $siteID . '.json'
        ]);
        fclose($tempFile2);

        error_log('Successfully uploaded ' . $type . ' to GCS');
    } catch (Exception $e) {
        error_log('Error uploading ' . $type . ' to GCS: ' . $e->getMessage());
    }
}

